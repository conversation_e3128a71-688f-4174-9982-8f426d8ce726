# 提款申请号查询功能使用指南

## 功能概述

main.js 脚本现在支持自动化查询提款申请号功能。在完成登录和页面跳转后，脚本会提示用户输入需要查询的提款申请号，并自动执行查询操作。

## 功能特性

### ✨ 核心功能
- 🔍 **批量查询**: 支持同时查询多个提款申请号
- 📝 **智能解析**: 自动解析空格分隔的提款申请号
- 🔄 **去重处理**: 自动去除重复的申请号
- 📊 **进度跟踪**: 实时显示查询进度和结果统计
- ⚡ **错误处理**: 单个查询失败不影响其他查询
- 🕐 **智能延迟**: 查询间自动添加延迟，避免请求过于频繁

### 🛡️ 安全特性
- 输入验证和清理
- 超时保护机制
- 详细的错误日志记录
- 自动截图保存（出错时）

## 使用流程

### 1. 启动脚本
```bash
# 使用 pnpm（推荐）
pnpm run script -p main.js

# 或使用 npm
npm start run main

# 或直接运行
node src/scripts/main.js
```

### 2. 自动化登录流程
脚本会自动执行以下步骤：
1. 初始化浏览器
2. 访问登录页面
3. 等待登录表单加载
4. 填写登录信息（从配置文件读取）
5. 点击登录按钮
6. 等待登录成功（支持验证码手动输入）
7. 确保跳转到目标页面

### 3. 提款申请号查询
登录成功后，脚本会：
1. 提示用户输入提款申请号
2. 解析和验证输入的申请号
3. 逐个执行查询操作
4. 显示查询结果和统计信息

## 输入格式说明

### 支持的输入格式

#### 单个提款申请号
```
A123456789
```

#### 多个提款申请号（空格分隔）
```
A123456789 B987654321 C555666777
```

#### 带有多余空格的输入
```
  A123456789   B987654321   C555666777  
```

### 输入处理规则
- ✅ 自动去除前后空格
- ✅ 支持多个连续空格作为分隔符
- ✅ 自动去除重复的申请号
- ✅ 忽略空的输入项

### 示例输入输出

| 用户输入 | 解析结果 | 说明 |
|---------|---------|------|
| `A123 B456 C789` | `['A123', 'B456', 'C789']` | 正常的多个申请号 |
| `  A123   B456  ` | `['A123', 'B456']` | 自动去除多余空格 |
| `A123 A123 B456` | `['A123', 'B456']` | 自动去重 |
| `SINGLE123` | `['SINGLE123']` | 单个申请号 |
| ` ` | `[]` | 空输入，脚本结束 |

## 查询过程详解

### 查询步骤
对于每个提款申请号，脚本会执行：

1. **定位输入框**: 查找 `placeholder="请输入提款申请号"` 的输入元素
2. **清空输入框**: 清除之前的内容
3. **输入申请号**: 填入当前要查询的申请号
4. **点击查询**: 点击 `.search-btns .el-button.el-button--info.el-button--mini` 按钮
5. **等待结果**: 等待页面加载完成
6. **记录结果**: 记录查询成功或失败

### 查询间隔
- 每次查询之间自动等待 2 秒
- 避免对服务器造成过大压力
- 提高查询成功率

## 日志输出示例

```
2024-01-20 10:30:15 [STEP] 📋 [步骤 8] 获取用户输入
请输入需要查询的提款申请号（多个号码用空格分隔）: A123 B456 C789

2024-01-20 10:30:20 [STEP] 📋 [步骤 9] 解析提款申请号
2024-01-20 10:30:20 [INFO] 解析到 3 个提款申请号 {"numbers":["A123","B456","C789"]}

2024-01-20 10:30:20 [STEP] 📋 [步骤 8] 开始处理 3 个提款申请号
2024-01-20 10:30:20 [INFO] 处理进度: 1/3 - A123
2024-01-20 10:30:20 [INFO] 开始查询提款申请号: A123
2024-01-20 10:30:22 [SUCCESS] ✅ 提款申请号 A123 查询完成

2024-01-20 10:30:24 [INFO] 处理进度: 2/3 - B456
2024-01-20 10:30:24 [INFO] 开始查询提款申请号: B456
2024-01-20 10:30:26 [SUCCESS] ✅ 提款申请号 B456 查询完成

2024-01-20 10:30:28 [INFO] 处理进度: 3/3 - C789
2024-01-20 10:30:28 [INFO] 开始查询提款申请号: C789
2024-01-20 10:30:30 [SUCCESS] ✅ 提款申请号 C789 查询完成

2024-01-20 10:30:30 [SUCCESS] ✅ 所有提款申请号处理完成 {"总数":3,"成功":3,"失败":0,"失败的申请号":[]}
2024-01-20 10:30:30 [SUCCESS] ✅ 所有查询任务执行完成
```

## 错误处理

### 常见错误及解决方案

#### 1. 输入框未找到
```
错误: 查询提款申请号 A123 失败 TimeoutError: Timeout 10000ms exceeded.
```
**解决方案**: 检查页面是否正确加载，确认输入框选择器是否正确

#### 2. 查询按钮未找到
```
错误: 查询提款申请号 A123 失败 TimeoutError: Timeout 5000ms exceeded.
```
**解决方案**: 检查查询按钮选择器是否正确，确认按钮是否可见

#### 3. 网络超时
```
错误: 查询提款申请号 A123 失败 TimeoutError: Timeout 15000ms exceeded.
```
**解决方案**: 检查网络连接，可能需要增加超时时间

### 错误恢复机制
- 单个查询失败不会中断整个流程
- 失败的申请号会被记录在最终统计中
- 自动保存错误截图用于调试

## 配置选项

可以在 `data/config.json` 中调整相关配置：

```json
{
  "timeout": 30000,           // 页面操作超时时间
  "slowMo": 50,              // 操作延迟
  "headless": false,         // 是否无头模式
  "customSettings": {
    "account": "your_account",
    "userPassword": "your_password"
  }
}
```

## 技术实现细节

### 核心函数

1. **getUserInput()**: 获取用户输入
2. **parseWithdrawalNumbers()**: 解析提款申请号
3. **searchSingleWithdrawal()**: 查询单个申请号
4. **processWithdrawalNumbers()**: 批量处理申请号

### Playwright 最佳实践
- 使用 `page.locator()` 进行元素定位
- 使用 `locator.waitFor()` 确保元素可见
- 使用 `locator.fill()` 进行输入操作
- 使用 `page.waitForLoadState()` 等待页面加载

## 注意事项

1. **登录凭据**: 确保配置文件中的账号密码正确
2. **验证码处理**: 如果出现验证码，请在60秒内手动输入
3. **网络稳定**: 确保网络连接稳定，避免查询中断
4. **页面元素**: 如果页面结构发生变化，可能需要更新选择器
5. **查询频率**: 避免过于频繁的查询，以免被系统限制

## 故障排除

如果遇到问题，请检查：
1. 配置文件是否正确
2. 网络连接是否正常
3. 页面元素选择器是否有效
4. 查看日志文件获取详细错误信息
5. 检查错误截图了解页面状态
