# 网页自动化脚本工具

一个基于 Node.js + Playwright 的网页自动化脚本框架，支持配置管理、执行记录、自动去重等功能。

## 特性

- 🚀 简单易用的项目结构，新手友好
- 📝 完善的日志系统
- 🔧 灵活的配置管理
- 📊 执行记录管理，自动去重
- 📸 自动截图功能
- 📦 支持打包成 exe 文件

## 安装

```bash
# 克隆项目
git clone <repository-url>
cd gf-auto-archive

# 安装依赖（推荐使用 pnpm）
pnpm install

# 或使用 npm
npm install
```

## 使用方法

### 1. 运行脚本

有多种方式运行脚本：

#### 方式一：使用 pnpm run script（推荐）
```bash
# 运行指定脚本
pnpm run script -p example.js

# 运行其他脚本
pnpm run script -p table-fetch.js

# 也可以省略 .js 后缀
pnpm run script -p example
```

#### 方式二：使用主程序
```bash
# 查看所有可用脚本
npm start list

# 运行指定脚本
npm start run example
```

#### 方式三：直接运行脚本文件
```bash
node src/scripts/example.js
```

### 2. 配置管理

#### 查看当前配置
```bash
npm start config
```

#### 修改配置
编辑 `data/config.json` 文件：

```json
{
  "fetchCount": 5,           // 每次处理的条数
  "headless": false,         // 是否无头模式
  "slowMo": 50,             // 操作延迟（毫秒）
  "timeout": 30000,         // 超时时间
  "customSettings": {       // 自定义配置
    "username": "",
    "password": "",
    "url": "https://example.com"
  }
}
```

#### 重置配置
```bash
npm start config-reset
```

### 3. 开发新脚本

在 `src/scripts/` 目录下创建新的脚本文件：

```javascript
const { createBrowser, createPage, closeBrowser } = require('../utils/browser');
const logger = require('../utils/logger');
const config = require('../utils/config');
const RecordManager = require('../utils/record');

async function run() {
  // 1. 初始化
  const configInstance = config.getInstance();
  const records = new RecordManager('your-script-name');
  
  let browser = null;
  let page = null;
  
  try {
    // 2. 创建浏览器
    browser = await createBrowser();
    page = await createPage(browser);
    
    // 3. 执行自动化任务
    await page.goto('https://example.com');
    
    // 4. 获取数据
    const items = [/* 你的数据 */];
    
    // 5. 过滤未处理的项目
    const fetchCount = configInstance.get('fetchCount', 5);
    const unprocessedItems = records.getUnprocessedItems(items, fetchCount);
    
    // 6. 处理项目
    for (const item of unprocessedItems) {
      try {
        // 处理逻辑
        
        // 记录成功
        records.addRecord(item, true);
      } catch (error) {
        // 记录失败
        records.addRecord(item, false, { error: error.message });
      }
    }
    
  } finally {
    await closeBrowser(browser);
  }
}

module.exports = { run };

// 支持直接运行
if (require.main === module || process.env.SCRIPT_RUNNER === 'true') {
  run().catch(error => {
    logger.error('脚本运行失败', error);
    process.exit(1);
  });
}
```

## 项目结构

```
gf-auto-archive/
├── src/
│   ├── index.js          # 主入口文件
│   ├── scripts/          # 脚本目录
│   │   ├── example.js    # 示例脚本
│   │   └── ...          # 其他脚本
│   └── utils/            # 工具类
│       ├── browser.js    # 浏览器管理
│       ├── config.js     # 配置管理
│       ├── logger.js     # 日志工具
│       └── record.js     # 记录管理
├── data/                 # 数据目录
│   ├── config.json       # 配置文件
│   └── records/          # 执行记录
├── logs/                 # 日志文件
├── screenshots/          # 截图文件
├── run.js               # 脚本运行器
├── build.js             # 打包脚本
├── package.json
└── README.md
```

## 核心工具说明

### 1. 浏览器管理 (browser.js)
- `createBrowser(options)`: 创建浏览器实例
- `createPage(browser)`: 创建新页面
- `closeBrowser(browser)`: 关闭浏览器

### 2. 配置管理 (config.js)
- `getInstance()`: 获取配置实例
- `get(key, defaultValue)`: 获取配置项
- `set(key, value)`: 设置配置项
- `reset()`: 重置配置

### 3. 日志工具 (logger.js)
- `info(message, meta)`: 信息日志
- `error(message, error)`: 错误日志
- `warn(message, meta)`: 警告日志
- `scriptStart(name)`: 记录脚本开始
- `scriptEnd(name, success, duration)`: 记录脚本结束
- `step(name, number)`: 记录步骤

### 4. 记录管理 (record.js)
- `new RecordManager(scriptName)`: 创建记录管理器
- `isProcessed(itemId)`: 检查是否已处理
- `addRecord(item, success, extra)`: 添加记录
- `getUnprocessedItems(items, limit)`: 获取未处理项目
- `getStatistics()`: 获取统计信息

## 打包发布

### 打包成 exe
```bash
npm run build
```

打包后的文件在 `dist/` 目录：
- `gf-auto-archive.exe`: 可执行文件
- `data/config.json`: 配置文件
- `README.txt`: 使用说明

### 使用打包后的程序
```bash
# 查看帮助
gf-auto-archive.exe --help

# 运行脚本
gf-auto-archive.exe run example

# 查看配置
gf-auto-archive.exe config
```

## 注意事项

1. **首次运行**: 可能需要下载 Chromium 浏览器
2. **配置文件**: 会自动创建默认配置文件
3. **执行记录**: 按日期保存，自动去重
4. **日志文件**: 自动轮转，最多保留 5 个文件
5. **截图文件**: 需要定期清理

## 开发建议

1. **命名规范**: 脚本文件使用小写字母和连字符
2. **错误处理**: 始终使用 try-catch 处理错误
3. **资源清理**: 确保在 finally 中关闭浏览器
4. **日志记录**: 记录关键步骤和错误信息
5. **配置优先**: 尽量使用配置文件中的参数

## 故障排除

### 脚本找不到
- 检查脚本路径是否正确
- 确保脚本文件存在于 `src/scripts/` 目录

### 浏览器启动失败
- 检查是否安装了 Playwright
- 尝试重新安装: `npx playwright install chromium`

### 配置无法保存
- 检查 `data/` 目录权限
- 确保配置文件格式正确（JSON）

## 贡献指南

欢迎提交 Issue 和 Pull Request！

## 许可证

ISC
