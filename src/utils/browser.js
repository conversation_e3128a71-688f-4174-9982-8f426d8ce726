const { chromium } = require('playwright');
const config = require('./config');
const logger = require('./logger');
const chalk = require('chalk');
const inquirer = require('inquirer');

/**
 * 支持的浏览器 channel 列表
 */
const BROWSER_CHANNELS = [
  'chrome', // Google Chrome
  'msedge', // Microsoft Edge
  'chrome-beta', // Chrome Beta
  'chrome-dev', // Chrome Dev
  'chrome-canary', // Chrome Canary
  'msedge-beta', // Edge Beta
  'msedge-dev', // Edge Dev
  'msedge-canary', // Edge Canary
];

/**
 * 尝试使用指定的 channel 启动浏览器
 * @param {string} channel - 浏览器 channel
 * @param {Object} options - 启动选项
 * @param {boolean} showSpinner - 是否显示spinner
 * @returns {Promise<Browser|null>} 浏览器实例或 null
 */
async function tryLaunchWithChannel(channel, options, showSpinner = false) {
  try {
    if (showSpinner) {
      logger.updateSpinner(`正在启动 ${chalk.cyan(channel)} 浏览器...`);
    } else {
      logger.debug(`尝试使用 ${channel} 启动浏览器...`);
    }

    const browser = await chromium.launch({ ...options, channel });

    if (showSpinner) {
      logger.succeedSpinner(`成功启动 ${chalk.green(channel)} 浏览器`);
    } else {
      logger.success(`成功使用 ${channel} 启动浏览器`);
    }

    return browser;
  } catch (error) {
    if (showSpinner) {
      // 不要fail spinner，让主函数处理
      logger.debug(`${channel} 启动失败: ${error.message}`);
    } else {
      logger.debug(`${channel} 启动失败: ${error.message}`);
    }
    return null;
  }
}

/**
 * 显示浏览器选择菜单
 * @param {Array} availableBrowsers - 可用的浏览器列表
 * @returns {Promise<string>} 选择的浏览器
 */
async function showBrowserSelection(availableBrowsers) {
  const choices = availableBrowsers.map((browser) => ({
    name: `${getBrowserDisplayName(browser)} ${chalk.gray(`(${browser})`)}`,
    value: browser,
  }));

  choices.push({
    name: `${chalk.yellow('Playwright 内置浏览器')} ${chalk.gray(
      '(chromium)',
    )}`,
    value: 'chromium',
  });

  const { selectedBrowser } = await inquirer.prompt([
    {
      type: 'list',
      name: 'selectedBrowser',
      message: '检测到多个可用浏览器，请选择要使用的浏览器:',
      choices,
      default: availableBrowsers[0],
    },
  ]);

  return selectedBrowser;
}

/**
 * 获取浏览器显示名称
 * @param {string} channel - 浏览器channel
 * @returns {string} 显示名称
 */
function getBrowserDisplayName(channel) {
  const displayNames = {
    chrome: '🌐 Google Chrome',
    msedge: '🔷 Microsoft Edge',
    'chrome-beta': '🌐 Chrome Beta',
    'chrome-dev': '🌐 Chrome Dev',
    'chrome-canary': '🌐 Chrome Canary',
    'msedge-beta': '🔷 Edge Beta',
    'msedge-dev': '🔷 Edge Dev',
    'msedge-canary': '🔷 Edge Canary',
    chromium: '🎭 Playwright Chromium',
  };
  return displayNames[channel] || channel;
}

/**
 * 检测可用的浏览器
 * @param {Object} browserOptions - 浏览器选项
 * @returns {Promise<Array>} 可用的浏览器列表
 */
async function detectAvailableBrowsers(browserOptions) {
  const availableBrowsers = [];
  const channelsToCheck = ['chrome', 'msedge', 'chrome-beta', 'msedge-beta'];

  logger.startSpinner('正在检测可用的浏览器...');

  for (const channel of channelsToCheck) {
    try {
      const browser = await chromium.launch({ ...browserOptions, channel });
      await browser.close();
      availableBrowsers.push(channel);
      logger.updateSpinner(`检测到 ${getBrowserDisplayName(channel)}`);
    } catch (error) {
      // 浏览器不可用，继续检测下一个
    }
  }

  logger.stopSpinner();
  return availableBrowsers;
}

/**
 * 创建浏览器实例
 * @param {Object} options - 浏览器启动选项
 * @returns {Promise<Browser>} 浏览器实例
 */
async function createBrowser(options = {}) {
  const configData = config.getInstance();

  const defaultOptions = {
    headless: configData.get('headless', false),
    slowMo: configData.get('slowMo', 50),
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-pdf-extension',
      '--proxy-auto-detect', // 使用系统代理设置
      '--ignore-certificate-errors',
    ],
  };

  const browserOptions = { ...defaultOptions, ...options };
  const preferredBrowser = configData.get('browser', 'auto');

  // 显示浏览器配置信息
  logger.table(
    {
      浏览器模式: browserOptions.headless ? '无头模式' : '有界面模式',
      操作延迟: `${browserOptions.slowMo}ms`,
      首选浏览器: preferredBrowser === 'auto' ? '自动检测' : preferredBrowser,
    },
    '浏览器配置',
  );

  // 如果指定了特定的浏览器
  if (
    preferredBrowser !== 'auto' &&
    BROWSER_CHANNELS.includes(preferredBrowser)
  ) {
    logger.startSpinner(
      `正在启动指定的浏览器 ${getBrowserDisplayName(preferredBrowser)}...`,
    );

    try {
      const browser = await tryLaunchWithChannel(
        preferredBrowser,
        browserOptions,
        true,
      );
      if (browser) {
        logger.succeedSpinner(
          `成功启动 ${getBrowserDisplayName(preferredBrowser)}`,
        );
        return browser;
      }
    } catch (error) {
      logger.failSpinner(`指定的浏览器 ${preferredBrowser} 启动失败`);
      logger.warn('将尝试自动检测其他可用浏览器...');
    }
  }

  // 自动检测可用的系统浏览器
  if (preferredBrowser === 'auto' || preferredBrowser === 'interactive') {
    const availableBrowsers = await detectAvailableBrowsers(browserOptions);

    if (availableBrowsers.length === 0) {
      logger.warn('未检测到任何系统浏览器，将使用 Playwright 内置浏览器');
    } else if (availableBrowsers.length === 1) {
      // 只有一个可用浏览器，直接使用
      const selectedBrowser = availableBrowsers[0];
      logger.startSpinner(
        `正在启动 ${getBrowserDisplayName(selectedBrowser)}...`,
      );

      const browser = await tryLaunchWithChannel(
        selectedBrowser,
        browserOptions,
        true,
      );
      if (browser) {
        configData.set('lastSuccessfulChannel', selectedBrowser);
        return browser;
      }
    } else if (preferredBrowser === 'interactive') {
      // 交互式选择浏览器
      const selectedBrowser = await showBrowserSelection(availableBrowsers);

      if (selectedBrowser === 'chromium') {
        // 用户选择了内置浏览器
        logger.startSpinner('正在启动 Playwright 内置浏览器...');
      } else {
        logger.startSpinner(
          `正在启动 ${getBrowserDisplayName(selectedBrowser)}...`,
        );
        const browser = await tryLaunchWithChannel(
          selectedBrowser,
          browserOptions,
          true,
        );
        if (browser) {
          configData.set('lastSuccessfulChannel', selectedBrowser);
          return browser;
        }
      }
    } else {
      // 自动选择第一个可用的浏览器
      logger.info(
        `检测到 ${
          availableBrowsers.length
        } 个可用浏览器，使用 ${getBrowserDisplayName(availableBrowsers[0])}`,
      );

      for (const channel of availableBrowsers) {
        logger.startSpinner(`正在启动 ${getBrowserDisplayName(channel)}...`);
        const browser = await tryLaunchWithChannel(
          channel,
          browserOptions,
          true,
        );
        if (browser) {
          configData.set('lastSuccessfulChannel', channel);
          return browser;
        }
      }
    }
  }

  // 最后尝试使用 Playwright 内置浏览器
  logger.startSpinner('正在启动 Playwright 内置浏览器...');

  try {
    const browser = await chromium.launch(browserOptions);
    logger.succeedSpinner('成功启动 Playwright 内置浏览器');
    return browser;
  } catch (error) {
    logger.failSpinner('浏览器启动失败');

    // 显示详细的错误信息和建议
    logger.separator('═', 60, 'red');
    logger.error('浏览器启动失败', error);
    logger.separator('─', 60, 'yellow');

    logger.table(
      [
        '1. 安装 Chrome 或 Edge 浏览器',
        '2. 运行 npx playwright install chromium 安装内置浏览器',
        '3. 在 config.json 中设置 "browser": "chrome" 或 "browser": "msedge"',
        '4. 检查系统权限和防火墙设置',
      ],
      '解决建议',
    );

    logger.separator('═', 60, 'red');
    throw error;
  }
}

/**
 * 创建新页面并设置默认配置
 * @param {Browser} browser - 浏览器实例
 * @returns {Promise<import('playwright').Page>} 页面实例
 */
async function createPage(browser) {
  const configData = config.getInstance();

  logger.startSpinner('正在创建新页面...');

  try {
    const page = await browser.newPage();

    // 设置默认超时时间
    const timeout = configData.get('timeout', 30000);
    page.setDefaultTimeout(timeout);
    page.setDefaultNavigationTimeout(timeout);

    // 设置视口大小
    const viewport = {
      width: configData.get('viewportWidth', 1920),
      height: configData.get('viewportHeight', 1080),
    };
    await page.setViewportSize(viewport);

    // 设置用户代理（可选）
    const userAgent = configData.get('userAgent');
    if (userAgent) {
      await page.setUserAgent(userAgent);
    }

    logger.succeedSpinner('页面创建成功');

    // 显示页面配置信息
    logger.table(
      {
        超时时间: `${timeout}ms`,
        视口大小: `${viewport.width}x${viewport.height}`,
        用户代理: userAgent || '默认',
      },
      '页面配置',
    );

    return page;
  } catch (error) {
    logger.failSpinner('页面创建失败');
    logger.error('创建页面时出错', error);
    throw error;
  }
}

/**
 * 安全关闭浏览器
 * @param {Browser} browser - 浏览器实例
 */
async function closeBrowser(browser) {
  if (!browser) {
    logger.debug('浏览器实例为空，无需关闭');
    return;
  }

  logger.startSpinner('正在关闭浏览器...');

  try {
    // 获取所有上下文并关闭页面
    const contexts = browser.contexts();
    let totalPages = 0;

    for (const context of contexts) {
      const pages = context.pages();
      totalPages += pages.length;
      if (pages.length > 0) {
        logger.updateSpinner(`正在关闭 ${pages.length} 个页面...`);
        await Promise.all(pages.map((page) => page.close().catch(() => {})));
      }
    }

    // 关闭浏览器
    await browser.close();
    logger.succeedSpinner('浏览器已安全关闭');
  } catch (error) {
    logger.failSpinner('关闭浏览器时出错');
    logger.error('关闭浏览器时出错', error);

    // 尝试强制关闭
    try {
      await browser.close();
      logger.warn('已强制关闭浏览器');
    } catch (forceError) {
      logger.error('强制关闭浏览器也失败了', forceError);
    }
  }
}

/**
 * 获取浏览器信息
 * @param {Browser} browser - 浏览器实例
 * @returns {Promise<Object>} 浏览器信息
 */
async function getBrowserInfo(browser) {
  try {
    const version = await browser.version();
    const userAgent = await browser
      .newPage()
      .then((page) =>
        page.evaluate(() => navigator.userAgent).finally(() => page.close()),
      );

    return {
      version,
      userAgent: userAgent.split(' ')[0], // 只取第一部分
    };
  } catch (error) {
    logger.debug('获取浏览器信息失败', error);
    return {
      version: '未知',
      userAgent: '未知',
    };
  }
}

module.exports = {
  createBrowser,
  createPage,
  closeBrowser,
  getBrowserInfo,
  getBrowserDisplayName,
  BROWSER_CHANNELS,
};
