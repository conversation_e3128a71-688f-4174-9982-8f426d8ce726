const path = require('path');
const fs = require('fs');
const logger = require('./logger');

/**
 * 安全地创建目录，添加错误处理
 * @param {string} dirPath - 目录路径
 * @returns {boolean} - 是否成功创建或目录已存在
 */
function ensureDirectoryExists(dirPath) {
  try {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
    return true;
  } catch (error) {
    logger.error(`创建目录失败: ${dirPath}`, error);
    return false;
  }
}

const isFilePage = (url) => {
  return (
    url.includes('/file/get-file-with-token/') || url.includes('/file/getFile/')
  );
};

/**
 * PDF下载工具类
 * 用于处理PDF预览页面的拦截和文件下载
 */
class PDFDownloader {
  constructor() {
    this.downloadPromise = null;
    this.isListening = false;
    this.downloadCompleted = false;
    // 保存监听器引用，用于清理
    this.pageListener = null;
    this.responseListener = null;
  }

  /**
   * 设置PDF下载拦截器
   * @param {Page} page - Playwright页面对象
   * @param {string} downloadDir - 下载目录路径
   * @param {string} fileName - 文件名（不包含扩展名）
   * @returns {Promise<void>}
   */
  async setupPDFInterceptor(page, downloadDir, fileName) {
    const context = page.context();

    // 避免重复监听
    if (this.isListening) {
      return;
    }

    this.isListening = true;

    // 保存监听器引用
    this.pageListener = async (newPage) => {
      try {
        await newPage.waitForLoadState('load', { timeout: 10000 });
        const url = newPage.url();
        if (isFilePage(url)) {
          await this.handlePDFDownload(newPage, downloadDir, fileName);
        }
      } catch (newPageError) {
        logger.warn(`处理新页面失败: ${newPageError.message}`);
      }
    };

    this.responseListener = async (response) => {
      try {
        const url = response.url();
        if (isFilePage(url) && response.status() === 200) {
          const contentType = response.headers()['content-type'];
          if (contentType && contentType.includes('application/pdf')) {
            await this.handleDirectPDFDownload(
              page,
              response,
              downloadDir,
              fileName,
            );
          }
        }
      } catch (responseError) {
        logger.warn(`⚠️ 处理响应失败: ${responseError.message}`);
      }
    };

    // 绑定监听器
    context.on('page', this.pageListener);
    page.on('response', this.responseListener);

    // 保存 page 和 context 引用用于清理
    this.page = page;
    this.context = context;
  }

  /**
   * 处理PDF页面下载
   */
  // async handlePDFDownload(pdfPage, downloadDir, fileName) {
  //   // 在 handlePDFDownload 方法中，直接通过响应获取文件
  //   try {
  //     // 重新请求当前页面获取PDF内容
  //     const response = await pdfPage.goto(pdfPage.url());

  //     if (response && response.status() === 200) {
  //       const contentType = response.headers()['content-type'];
  //       if (contentType && contentType.includes('application/pdf')) {
  //         const buffer = await response.body();

  //         const fullFileName = fileName.endsWith('.pdf')
  //           ? fileName
  //           : `${fileName}.pdf`;
  //         const downloadPath = path.join(downloadDir, fullFileName);

  //         // 确保目录存在
  //         const dir = path.dirname(downloadPath);
  //         if (!ensureDirectoryExists(dir)) {
  //           throw new Error(`无法创建下载目录: ${dir}`);
  //         }

  //         await fs.writeFileSync(downloadPath, buffer);
  //         logger.success(`${fileName} 下载成功`);

  //         await pdfPage.close();
  //         return;
  //       }
  //     }
  //   } catch (error) {
  //     logger.warn(`下载失败: ${error.message}`);
  //   }
  // }

  /**
   * 处理PDF页面下载
   */
  async handlePDFDownload(pdfPage, downloadDir, fileName) {
    try {
      // 设置下载处理
      this.downloadPromise = pdfPage.waitForEvent('download', {
        timeout: 30000,
      });

      // 触发下载
      await pdfPage.evaluate(() => {
        // 创建一个隐藏的下载链接
        const link = document.createElement('a');
        link.href = window.location.href;
        link.download = 'document.pdf';
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      });

      // 等待下载完成
      try {
        const download = await this.downloadPromise;
        const name = fileName.trim();
        const fullFileName = name.endsWith('.pdf') ? name : `${name}.pdf`;
        const downloadPath = path.join(downloadDir, fullFileName);
        await download.saveAs(downloadPath);
        logger.success(`${name} 下载成功`);
      } catch (downloadError) {
        logger.warn(`下载文件失败: ${downloadError.message}`);
      }

      // 关闭PDF预览页面
      await pdfPage.close();
    } catch (error) {
      logger.warn(`处理PDF下载失败: ${error.message}`);
    }
  }

  /**
   * 处理直接PDF下载（通过响应拦截）
   */
  async handleDirectPDFDownload(page, response, downloadDir, fileName) {
    try {
      // 获取PDF内容
      const buffer = await response.body();

      // 保存文件
      const fullFileName = fileName.endsWith('.pdf')
        ? fileName
        : `${fileName}.pdf`;
      const downloadPath = path.join(downloadDir, fullFileName);

      // 确保目录存在
      const dir = path.dirname(downloadPath);
      if (!ensureDirectoryExists(dir)) {
        throw new Error(`无法创建下载目录: ${dir}`);
      }

      fs.writeFileSync(downloadPath, buffer);

      // 立即停止页面加载 - 使用字符串形式
      await page.evaluate(`() => {
        window.stop();
        document.open();
        document.write('<html><body><h1>文件下载完成</h1><script>window.close();</script></body></html>');
        document.close();
      }`);

      // 标记下载完成
      this.downloadCompleted = true;
    } catch (error) {
      logger.warn(`直接下载PDF失败: ${error.message}`);
    }
  }

  /**
   * 点击下载链接并处理PDF下载
   * @param {Locator} downloadLinkElement - 下载链接元素
   * @param {Page} page - Playwright页面对象
   * @param {string} downloadDir - 下载目录路径
   * @param {string} fileName - 文件名（不包含扩展名）
   * @param {number} waitTime - 等待时间（毫秒），默认3000
   * @returns {Promise<boolean>} 是否下载成功
   */
  async downloadPDF(
    downloadLocator,
    page,
    downloadDir,
    fileName,
    waitTime = 3000,
  ) {
    try {
      // 设置PDF拦截器
      await this.setupPDFInterceptor(page, downloadDir, fileName);

      const downloadLinkElement =
        typeof downloadLocator === 'string'
          ? page.locator(downloadLocator)
          : downloadLocator;

      await downloadLinkElement.waitFor({
        state: 'attached',
        timeout: 10000,
      });

      // 检查元素是否可见
      // const isVisible = await downloadLinkElement.isVisible();

      // * 原方法，发现click 不如evaluate 好用。直接改成 evaluate
      // if (isVisible) {
      //   // 元素可见，使用常规点击
      //   await downloadLinkElement.click({ force: true });
      // } else {
      //   await downloadLinkElement.evaluate((el) => el.click());
      // }

      // 新方法，需进一步验证
      // await downloadLinkElement.evaluate((el) => el.click());

      // await downloadLinkElement.evaluate(function (el) {
      //   el.click();
      // });

      try {
        await downloadLinkElement.click({ force: true });
      } catch (error) {
        logger.warn(
          `文件 ${fileName} 方案一下载失败，尝试方案二下载: ${error.message}`,
        );
        await downloadLinkElement.dispatchEvent('click');
      }

      // await downloadLinkElement.mouseup({ force: true });

      // 等待一段时间确保下载处理完成
      await page.waitForTimeout(waitTime);

      return true;
    } catch (error) {
      logger.warn(`PDF下载失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 重置监听状态
   */
  resetListening() {
    this.isListening = false;
  }

  /**
   * 清理资源
   */
  cleanup() {
    // 移除事件监听器
    if (this.pageListener && this.context) {
      this.context.off('page', this.pageListener);
    }
    if (this.responseListener && this.page) {
      this.page.off('response', this.responseListener);
    }

    // 重置状态
    this.downloadPromise = null;
    this.isListening = false;
    this.downloadCompleted = false;
    this.pageListener = null;
    this.responseListener = null;
    this.page = null;
    this.context = null;
  }
}

// 创建单例实例
const pdfDownloader = new PDFDownloader();

/**
 * 简化的PDF下载函数
 * @param {string} downloadSelector - 下载链接元素
 * @param {Page} page - Playwright页面对象
 * @param {string} downloadDir - 下载目录路径
 * @param {string} fileName - 文件名（不包含扩展名）
 * @param {number} waitTime - 等待时间（毫秒），默认3000
 * @returns {Promise<boolean>} 是否下载成功
 */
async function downloadPDFFile(
  downloadSelector,
  page,
  downloadDir,
  fileName,
  waitTime = 3000,
) {
  // 为每次下载创建独立的实例
  const downloader = new PDFDownloader();

  try {
    const result = await downloader.downloadPDF(
      downloadSelector,
      page,
      downloadDir,
      fileName,
      waitTime,
    );

    // 下载完成后清理资源
    downloader.cleanup();
    if (!result) {
      logger.warn(`${fileName}下载失败`);
    }
    return result;
  } catch (error) {
    // 出错时也要清理资源
    downloader.cleanup();
    logger.warn(`下载文件失败: ${error.message}`);
    throw error;
  }
}

/**
 * 重置PDF下载器状态
 */
function resetPDFDownloader() {
  pdfDownloader.resetListening();
}

/**
 * 清理PDF下载器资源
 */
function cleanupPDFDownloader() {
  pdfDownloader.cleanup();
}

/**
 * 从PDF预览URL下载文件（使用新页面下载方案）
 * @param {string} url - PDF预览地址
 * @param {Page} page - Playwright页面对象
 * @param {string} downloadDir - 下载目录路径
 * @param {string} fileName - 文件名（不包含扩展名）
 * @returns {Promise<boolean>} 是否下载成功
 */
async function downloadFromURL(url, page, downloadDir, fileName) {
  let newContext = null;
  let newPage = null;

  try {
    // 参数验证
    if (!url || !page || !downloadDir || !fileName) {
      throw new Error(
        `参数不完整: url=${url}, downloadDir=${downloadDir}, fileName=${fileName}`,
      );
    }

    // 从浏览器实例创建新的上下文和页面
    const browser = page.context().browser();
    newContext = await browser.newContext();
    newPage = await newContext.newPage();

    // 设置响应拦截器
    let downloadCompleted = false;

    newPage.on('response', async (response) => {
      try {
        const responseUrl = response.url();
        if (isFilePage(responseUrl) && response.status() === 200) {
          // 检查响应类型是否为PDF
          const contentType = response.headers()['content-type'];
          if (contentType && contentType.includes('application/pdf')) {
            // 直接处理PDF下载
            const buffer = await response.body();

            // 保存文件
            const fullFileName = `${fileName}.pdf`;
            const downloadPath = path.join(downloadDir, fullFileName);

            // 确保目录存在
            const dir = path.dirname(downloadPath);
            if (!ensureDirectoryExists(dir)) {
              throw new Error(`无法创建下载目录: ${dir}`);
            }

            fs.writeFileSync(downloadPath, buffer);

            downloadCompleted = true;

            logger.success(`${fullFileName}下载成功`);
          }
        }
      } catch (responseError) {
        logger.warn(`处理响应失败: ${responseError.message}`);
      }
    });

    // 访问PDF地址 - 使用专门的直接下载处理函数
    const navigationResult = await handleDirectDownloadNavigation(
      newPage,
      url,
      { timeout: 15000 },
    );

    if (navigationResult.isDirectDownload) {
      logger.info('检测到直接PDF下载，等待下载完成...');
    } else if (!navigationResult.success) {
      logger.warn('页面导航失败，但继续等待可能的下载事件');
    }

    // 等待下载完成
    let waitCount = 0;
    while (!downloadCompleted && waitCount < 20) {
      // 最多等待10秒
      await newPage.waitForTimeout(500);
      waitCount++;
    }

    if (downloadCompleted) {
      return true;
    } else {
      logger.warn(`PDF文件下载超时: ${fileName}`);
      return false;
    }
  } catch (error) {
    logger.warn(`从URL下载PDF失败: ${error.message}`);
    return false;
  } finally {
    // 确保新页面和上下文被关闭
    if (newPage) {
      try {
        await newPage.close();
      } catch (closeError) {
        logger.warn(`关闭新页面失败: ${closeError.message}`);
      }
    }

    if (newContext) {
      try {
        await newContext.close();
      } catch (closeError) {
        logger.warn(`关闭新上下文失败: ${closeError.message}`);
      }
    }
  }
}

/**
 * 通用文件下载函数 - 支持多种文件类型（PDF、PNG、JPG等）
 * @param {string|Locator} downloadSelector - 下载链接元素或URL
 * @param {Page} page - Playwright页面对象
 * @param {string} downloadDir - 下载目录路径
 * @param {string} fileName - 文件名（不包含扩展名）
 * @param {Object} options - 选项配置
 * @param {number} options.waitTime - 等待时间（毫秒），默认3000
 * @param {string} options.fileType - 强制指定文件类型，如 'pdf', 'png', 'jpg'
 * @returns {Promise<boolean>} 是否下载成功
 */
async function downloadFile(
  downloadSelector,
  page,
  downloadDir,
  fileName,
  options = {},
) {
  const { waitTime = 3000, fileType = null } = options;
  let newContext = null;
  let newPage = null;

  try {
    // 参数验证
    if (!downloadSelector || !page || !downloadDir || !fileName) {
      throw new Error(
        `参数不完整: downloadSelector=${downloadSelector}, downloadDir=${downloadDir}, fileName=${fileName}`,
      );
    }

    // 如果传入的是字符串URL，直接使用URL下载
    if (
      typeof downloadSelector === 'string' &&
      downloadSelector.startsWith('http')
    ) {
      return await downloadFileFromURL(
        downloadSelector,
        page,
        downloadDir,
        fileName,
        options,
      );
    }

    // 从浏览器实例创建新的上下文和页面
    const browser = page.context().browser();
    newContext = await browser.newContext();
    newPage = await newContext.newPage();

    // 设置响应拦截器
    let downloadCompleted = false;

    newPage.on('response', async (response) => {
      try {
        const responseUrl = response.url();
        if (isFilePage(responseUrl) && response.status() === 200) {
          const contentType = response.headers()['content-type'];
          const buffer = await response.body();

          // 根据Content-Type检测文件类型
          let extension = '';
          if (contentType) {
            if (contentType.includes('application/pdf')) {
              extension = '.pdf';
            } else if (contentType.includes('image/png')) {
              extension = '.png';
            } else if (
              contentType.includes('image/jpeg') ||
              contentType.includes('image/jpg')
            ) {
              extension = '.jpg';
            } else if (contentType.includes('image/gif')) {
              extension = '.gif';
            } else if (contentType.includes('image/webp')) {
              extension = '.webp';
            }
          }

          // 如果指定了文件类型，使用指定的类型
          if (fileType) {
            extension = `.${fileType}`;
          }

          // 如果无法检测到文件类型，默认使用PDF
          if (!extension) {
            extension = '.pdf';
            logger.warn(`无法检测文件类型，默认使用PDF格式: ${contentType}`);
          }

          // 保存文件
          const fullFileName = `${fileName}${extension}`;
          const downloadPath = path.join(downloadDir, fullFileName);

          // 确保目录存在
          const dir = path.dirname(downloadPath);
          if (!ensureDirectoryExists(dir)) {
            throw new Error(`无法创建下载目录: ${dir}`);
          }

          fs.writeFileSync(downloadPath, buffer);
          logger.success(`文件下载成功: ${fullFileName}`);

          downloadCompleted = true;
        }
      } catch (responseError) {
        logger.warn(`处理响应失败: ${responseError.message}`);
      }
    });

    // 监听新页面创建（如果点击链接会打开新页面）
    const newPagePromise = page.context().waitForEvent('page');

    // 点击下载链接
    if (typeof downloadSelector === 'string') {
      // 如果是选择器字符串
      await page.locator(downloadSelector).click();
    } else {
      // 如果是Locator对象
      await downloadSelector.click();
    }

    // 等待新页面打开
    try {
      const targetPage = await newPagePromise;
      await targetPage.waitForLoadState('networkidle', { timeout: 15000 });

      // 关闭新打开的页面
      await targetPage.close();
    } catch (pageError) {
      logger.warn(`处理新页面失败: ${pageError.message}`);
    }

    // 等待下载完成
    let waitCount = 0;
    const maxWaitCount = Math.ceil(waitTime / 500);
    while (!downloadCompleted && waitCount < maxWaitCount) {
      await page.waitForTimeout(500);
      waitCount++;
    }

    if (downloadCompleted) {
      return true;
    } else {
      logger.warn(`文件下载超时: ${fileName}`);
      return false;
    }
  } catch (error) {
    logger.warn(`下载文件失败: ${error.message}`);
    return false;
  } finally {
    // 确保新页面和上下文被关闭
    if (newPage) {
      try {
        await newPage.close();
      } catch (closeError) {
        logger.warn(`关闭新页面失败: ${closeError.message}`);
      }
    }

    if (newContext) {
      try {
        await newContext.close();
      } catch (closeError) {
        logger.warn(`关闭新上下文失败: ${closeError.message}`);
      }
    }
  }
}

/**
 * 处理直接下载场景的页面导航（简化版，专注于导航处理）
 * @param {Page} page - Playwright页面对象
 * @param {string} url - 要访问的URL
 * @param {Object} options - 导航选项
 * @returns {Promise<{success: boolean, isDirectDownload: boolean}>} 导航结果
 */
async function handleDirectDownloadNavigation(page, url, options = {}) {
  const { timeout = 10000 } = options;

  try {
    // 使用较短的超时时间，因为直接下载通常会很快中止导航
    await page.goto(url, {
      timeout: Math.min(timeout, 5000), // 最多等待5秒
      waitUntil: 'commit',
    });

    logger.info('页面导航成功完成');
    return { success: true, isDirectDownload: false };
  } catch (gotoError) {
    // 对于直接下载，ERR_ABORTED 是预期的行为
    if (
      gotoError.message.includes('ERR_ABORTED') ||
      gotoError.message.includes('net::ERR_ABORTED')
    ) {
      logger.info('检测到直接下载');
      // 给下载监听器一些时间来处理下载
      await page.waitForTimeout(2000);
      return { success: true, isDirectDownload: true };
    } else {
      logger.warn(`页面导航失败: ${gotoError.message}`);
      // 即使导航失败，也给下载事件一些时间
      await page.waitForTimeout(1000);
      return { success: false, isDirectDownload: false };
    }
  }
}

/**
 * 专门处理直接下载链接的简化函数
 * @param {string} url - 直接下载链接
 * @param {Page} page - Playwright页面对象
 * @param {string} downloadDir - 下载目录路径
 * @param {string} fileName - 文件名（不包含扩展名）
 * @param {Object} options - 选项配置
 * @returns {Promise<boolean>} 是否下载成功
 */
async function downloadDirectLink(
  url,
  page,
  downloadDir,
  fileName,
  options = {},
) {
  const { fileType = null, timeout = 10000 } = options;
  let newContext = null;
  let newPage = null;
  let downloadCompleted = false;

  try {
    // 从浏览器实例创建新的上下文和页面
    const browser = page.context().browser();
    newContext = await browser.newContext({ acceptDownloads: true });
    newPage = await newContext.newPage();

    // 监听下载事件（优先使用这个，因为它更可靠）
    newPage.on('download', async (download) => {
      try {
        if (downloadCompleted) return;

        const suggestedFilename = download.suggestedFilename();
        let extension = '';

        if (suggestedFilename) {
          const lastDotIndex = suggestedFilename.lastIndexOf('.');
          if (lastDotIndex > 0) {
            extension = suggestedFilename.substring(lastDotIndex);
          }
        }

        if (fileType) {
          extension = `.${fileType}`;
        }

        if (!extension) {
          extension = '.pdf'; // 默认扩展名
        }

        const fullFileName = fileName.endsWith(extension)
          ? fileName
          : `${fileName}${extension}`;
        const downloadPath = path.join(downloadDir, fullFileName);

        const dir = path.dirname(downloadPath);
        if (!ensureDirectoryExists(dir)) {
          throw new Error(`无法创建下载目录: ${dir}`);
        }

        await download.saveAs(downloadPath);
        logger.success(`直接下载成功: ${fullFileName}`);
        downloadCompleted = true;
      } catch (downloadError) {
        logger.warn(`下载事件处理失败: ${downloadError.message}`);
      }
    });

    // 直接访问下载链接，预期会触发下载或导航中止
    try {
      await newPage.goto(url, {
        timeout: Math.min(timeout, 5000),
        waitUntil: 'commit',
      });
    } catch (gotoError) {
      if (gotoError.message.includes('ERR_ABORTED')) {
        logger.info('直接下载链接导致导航中止（正常现象）');
      } else {
        logger.warn(`访问下载链接失败: ${gotoError.message}`);
      }
    }

    // 等待下载完成
    let waitCount = 0;
    const maxWaitCount = Math.ceil(timeout / 500);
    while (!downloadCompleted && waitCount < maxWaitCount) {
      await newPage.waitForTimeout(500);
      waitCount++;
    }

    return downloadCompleted;
  } catch (error) {
    logger.warn(`直接下载失败: ${error.message}`);
    return false;
  } finally {
    // 清理资源
    if (newPage) {
      try {
        await newPage.close();
      } catch (closeError) {
        logger.warn(`关闭新页面失败: ${closeError.message}`);
      }
    }

    if (newContext) {
      try {
        await newContext.close();
      } catch (closeError) {
        logger.warn(`关闭新上下文失败: ${closeError.message}`);
      }
    }
  }
}

/**
 * 从URL下载文件 - 支持多种文件类型
 * @param {string} url - 文件URL
 * @param {Page} page - Playwright页面对象
 * @param {string} downloadDir - 下载目录路径
 * @param {string} fileName - 文件名（不包含扩展名）
 * @param {Object} options - 选项配置
 * @returns {Promise<boolean>} 是否下载成功
 */
async function downloadFileFromURL(
  url,
  page,
  downloadDir,
  fileName,
  options = {},
) {
  const { fileType = null } = options;
  let newContext = null;
  let newPage = null;

  try {
    // 参数验证
    if (!url || !page || !downloadDir || !fileName) {
      throw new Error(
        `参数不完整: url=${url}, downloadDir=${downloadDir}, fileName=${fileName}`,
      );
    }

    // 从浏览器实例创建新的上下文和页面
    const browser = page.context().browser();
    newContext = await browser.newContext({ acceptDownloads: true });
    newPage = await newContext.newPage();

    // 设置响应拦截器
    let downloadCompleted = false;

    // 监听下载事件（用于直接下载链接）
    newPage.on('download', async (download) => {
      try {
        if (downloadCompleted) return;

        const suggestedFilename = download.suggestedFilename();
        let extension = '';

        if (suggestedFilename) {
          const lastDotIndex = suggestedFilename.lastIndexOf('.');
          if (lastDotIndex > 0) {
            extension = suggestedFilename.substring(lastDotIndex);
          }
        }

        if (fileType) {
          extension = `.${fileType}`;
        }

        if (!extension) {
          extension = '.pdf';
        }

        const fullFileName = fileName.endsWith(extension)
          ? fileName
          : `${fileName}${extension}`;
        const downloadPath = path.join(downloadDir, fullFileName);

        const dir = path.dirname(downloadPath);
        if (!ensureDirectoryExists(dir)) {
          throw new Error(`无法创建下载目录: ${dir}`);
        }

        await download.saveAs(downloadPath);
        logger.success(`文件下载成功: ${fullFileName}`);
        downloadCompleted = true;
      } catch (downloadError) {
        logger.warn(`下载事件处理失败: ${downloadError.message}`);
      }
    });

    newPage.on('response', async (response) => {
      try {
        const responseUrl = response.url();
        if (isFilePage(responseUrl) && response.status() === 200) {
          const contentType = response.headers()['content-type'];
          let buffer;
          try {
            buffer = await response.body();
          } catch (bodyError) {
            // 对于直接下载链接，response.body() 可能会失败，这是正常的
            logger.info(`跳过响应体获取`);
            return;
          }

          // 根据Content-Type检测文件类型
          let extension = '';

          if (contentType) {
            if (contentType.includes('application/pdf')) {
              extension = '.pdf';
            } else if (contentType.includes('image/png')) {
              extension = '.png';
            } else if (
              contentType.includes('image/jpeg') ||
              contentType.includes('image/jpg')
            ) {
              extension = '.jpg';
            } else if (contentType.includes('image/gif')) {
              extension = '.gif';
            } else if (contentType.includes('image/webp')) {
              extension = '.webp';
            }
          }

          // 如果指定了文件类型，使用指定的类型
          if (fileType) {
            extension = `.${fileType}`;
          }

          // 如果无法检测到文件类型，默认使用PDF
          if (!extension) {
            extension = '.pdf';
            logger.warn(`无法检测文件类型，默认使用PDF格式: ${contentType}`);
          }

          // 保存文件
          const fullFileName = fileName.endsWith(extension)
            ? fileName
            : `${fileName}${extension}`;
          const downloadPath = path.join(downloadDir, fullFileName);

          // 确保目录存在
          const dir = path.dirname(downloadPath);
          if (!ensureDirectoryExists(dir)) {
            throw new Error(`无法创建下载目录: ${dir}`);
          }

          fs.writeFileSync(downloadPath, buffer);
          logger.success(`文件下载成功: ${fullFileName}`);

          downloadCompleted = true;
        }
      } catch (responseError) {
        logger.warn(`处理响应失败: ${responseError.message}`);
      }
    });

    // 访问文件地址 - 使用专门的直接下载处理函数
    const navigationResult = await handleDirectDownloadNavigation(
      newPage,
      url,
      { timeout: 15000 },
    );

    if (navigationResult.isDirectDownload) {
      logger.info('检测到直接下载，等待下载完成...');
    } else if (!navigationResult.success) {
      logger.warn('页面导航失败，但继续等待可能的下载事件');
    }

    // 等待下载完成
    let waitCount = 0;
    while (!downloadCompleted && waitCount < 20) {
      // 最多等待10秒
      await newPage.waitForTimeout(500);
      waitCount++;
    }

    if (downloadCompleted) {
      return true;
    } else {
      logger.warn(`文件下载超时: ${fileName}`);
      return false;
    }
  } catch (error) {
    logger.warn(`从URL下载文件失败: ${error.message}`);
    return false;
  } finally {
    // 确保新页面和上下文被关闭
    if (newPage) {
      try {
        await newPage.close();
      } catch (closeError) {
        logger.warn(`关闭新页面失败: ${closeError.message}`);
      }
    }

    if (newContext) {
      try {
        await newContext.close();
      } catch (closeError) {
        logger.warn(`关闭新上下文失败: ${closeError.message}`);
      }
    }
  }
}

/**
 * 处理打印按钮点击事件
 * @param {Locator} printLocator - 打印按钮元素
 * @param {Page} page - Playwright页面对象
 * @param {string} savePath - 文件保存目录路径
 * @param {string} fileName - 文件名（不包含扩展名）
 * @returns {Promise<boolean>} 是否保存成功
 */
async function clickAndPrint(
  printLocator,
  page,
  savePath,
  fileName,
  namePrefix,
) {
  try {
    const printBtn =
      typeof printLocator === 'string'
        ? page.locator(printLocator)
        : printLocator;
    const context = page.context();

    // 监听新页面创建
    const newPagePromise = context.waitForEvent('page');

    // 点击打印按钮
    await printBtn.click();

    // 等待新页面打开
    const newPage = await newPagePromise;

    // 使用 CDP 禁用打印
    const client = await newPage.context().newCDPSession(newPage);

    // 拦截打印相关的 CDP 事件
    await client.send('Runtime.enable');
    await client.send('Page.enable');

    // 注入脚本阻止打印
    await client.send('Runtime.evaluate', {
      expression: `
        window.print = function() { console.log('Print intercepted'); };
        window.addEventListener('beforeprint', (e) => e.preventDefault(), true);
        window.addEventListener('afterprint', (e) => e.preventDefault(), true);
      `,
    });

    // 等待页面加载完成
    try {
      await newPage.waitForLoadState('networkidle', { timeout: 10000 });
    } catch (loadError) {
      logger.warn(`页面加载等待超时，尝试继续处理: ${loadError.message}`);
    }

    await newPage.waitForTimeout(3000);

    const newPageName = await newPage.title();
    const saveName = fileName ? fileName : namePrefix + newPageName;

    const pdfPath = path.join(savePath, `${saveName}.pdf`);

    // 生成PDF
    await newPage.pdf({
      path: pdfPath,
      format: 'A4',
      printBackground: true,
      margin: {
        top: '1cm',
        right: '1cm',
        bottom: '1cm',
        left: '1cm',
      },
    });

    logger.success(`${saveName} 下载成功`);

    await client.detach();
    await newPage.close();

    return true;
  } catch (error) {
    logger.warn(`处理打印按钮点击失败: ${fileName}`, error);
    return false;
  }
}

module.exports = {
  PDFDownloader,
  downloadPDFFile,
  downloadFile,
  downloadFileFromURL,
  downloadDirectLink,
  clickAndPrint,
  downloadFromURL,
  resetPDFDownloader,
  cleanupPDFDownloader,
};
