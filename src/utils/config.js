const fs = require('fs');
const path = require('path');

class Config {
  constructor() {
    // 在打包环境中，使用可执行文件所在目录的 data 文件夹
    const isPackaged = process.pkg !== undefined;
    if (isPackaged) {
      // 可执行文件外部的 data 目录
      this.configPath = path.join(
        path.dirname(process.execPath),
        'data',
        'config.json',
      );
    } else {
      // 开发环境使用原路径
      this.configPath = path.join(__dirname, '../../data/config.json');
    }

    this.data = {};
    this.load();
  }

  /**
   * 加载配置文件
   */
  load() {
    try {
      if (fs.existsSync(this.configPath)) {
        const content = fs.readFileSync(this.configPath, 'utf8');
        this.data = JSON.parse(content);
      } else {
        // 如果配置文件不存在，创建默认配置
        this.data = this.getDefaultConfig();
        this.save();
      }
    } catch (error) {
      console.error('加载配置文件失败:', error);
      this.data = this.getDefaultConfig();
    }
  }

  /**
   * 保存配置到文件
   */
  save() {
    try {
      // 确保目录存在
      const dir = path.dirname(this.configPath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      fs.writeFileSync(
        this.configPath,
        JSON.stringify(this.data, null, 2),
        'utf8',
      );
    } catch (error) {
      console.error('保存配置文件失败:', error);
    }
  }

  /**
   * 获取配置项
   * @param {string} key - 配置键名，支持点号分隔的嵌套路径
   * @param {*} defaultValue - 默认值
   */
  get(key, defaultValue = null) {
    const keys = key.split('.');
    let value = this.data;

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        return defaultValue;
      }
    }

    return value;
  }

  /**
   * 设置配置项
   * @param {string} key - 配置键名
   * @param {*} value - 配置值
   */
  set(key, value) {
    const keys = key.split('.');
    let obj = this.data;

    for (let i = 0; i < keys.length - 1; i++) {
      const k = keys[i];
      if (!(k in obj) || typeof obj[k] !== 'object') {
        obj[k] = {};
      }
      obj = obj[k];
    }

    obj[keys[keys.length - 1]] = value;
    this.save();
  }

  /**
   * 获取全部配置
   */
  getAll() {
    return { ...this.data };
  }

  /**
   * 获取默认配置
   */
  getDefaultConfig() {
    return {
      // 基础配置
      fetchCount: 5, // 每次获取条数
      headless: false, // 是否无头模式
      slowMo: 50, // 操作延迟（毫秒）
      timeout: 100000, // 超时时间（毫秒）
      navigationTimeout: 50000, // 导航超时时间
      retryTimes: 3, // 重试次数

      // 浏览器配置
      browser: 'chrome', // 浏览器类型: auto, chrome, msedge, chromium
      viewportWidth: 1920, // 视口宽度
      viewportHeight: 1080, // 视口高度

      // 日志配置
      logLevel: 'info', // 日志级别
      logToFile: true, // 是否记录到文件

      // 截图配置
      screenshotOnError: true, // 错误时截图

      // 用户自定义配置
      customSettings: {
        username: '', // 用户名
        password: '', // 密码
        url: '', // 目标网址
        archiveDir: '', // 归档目录
        logDir: '', // 日志目录
      },
    };
  }

  /**
   * 重置为默认配置
   */
  reset() {
    this.data = this.getDefaultConfig();
    this.save();
  }
}

// 单例模式
let instance = null;

module.exports = {
  getInstance() {
    if (!instance) {
      instance = new Config();
    }
    return instance;
  },
  Config,
};
