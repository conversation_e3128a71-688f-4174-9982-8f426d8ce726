const logger = require('../logger');

/**
 * 导航管理类 - 处理页面导航和状态管理
 */
class NavigationManager {
  constructor(page, options = {}) {
    this.page = page;
    this.options = {
      timeout: 30000,
      maxRetries: 3,
      retryDelay: 2000,
      ...options,
    };
  }

  /**
   * 导航到登录页面
   * @param {string} loginUrl - 登录URL
   */
  async goToLogin(loginUrl) {
    try {
      await this.page.goto(loginUrl, { waitUntil: 'networkidle' });
    } catch (error) {
      logger.error('导航到登录页面失败', error);
      throw error;
    }
  }

  async tabGoPage(page, targetUrl) {
    try {
      // 使用当前页面直接跳转

      await page.goto(targetUrl, {
        timeout: 30000,
        waitUntil: 'domcontentloaded',
      });

      // 等待页面完全加载
      await page.waitForLoadState('networkidle', { timeout: 15000 });
      return true;
    } catch (e) {
      logger.error(`跳转页面失败: ${e.message}`);
      return false;
    }
  }

  /**
   * 等待登录成功
   * @param {number} maxWaitTime - 最大等待时间（毫秒）
   */
  async waitForLoginSuccess(maxWaitTime = 120000) {
    const startTime = Date.now();
    const checkInterval = 1000;

    logger.info('等待登录成功...（如需验证码请在2分钟内输入）');

    while (Date.now() - startTime < maxWaitTime) {
      const currentUrl = this.page.url();

      // 检查是否在二次验证页面
      if (currentUrl.includes('/common/#/abnormal')) {
        logger.info('检测到二次验证页面，请输入验证码...');
        await this.page.waitForTimeout(checkInterval);
        continue;
      }

      // 检查是否已跳转到首页（登录成功）
      if (currentUrl.includes('/common/index.html#/index')) {
        logger.success('登录成功，已到达首页');
        return true;
      }

      // 检查是否还在登录页面
      if (currentUrl.includes('/login')) {
        await this.page.waitForTimeout(checkInterval);
        const elapsed = Math.floor((Date.now() - startTime) / 1000);
        if (elapsed % 15 === 0 && elapsed > 0) {
          logger.info(
            `等待登录中... (${elapsed}/${Math.floor(maxWaitTime / 1000)}秒)`,
          );
        }
        continue;
      }

      // 其他情况也等待一下
      await this.page.waitForTimeout(checkInterval);
    }

    throw new Error('登录超时');
  }

  /**
   * 确保跳转到目标页面
   * @param {string} targetUrl - 目标URL
   */
  async ensureTargetPage(targetUrl) {
    for (let i = 0; i < this.options.maxRetries; i++) {
      const currentUrl = this.page.url();

      if (currentUrl.includes('/operation-web/drawManage')) {
        return true;
      }

      logger.info(`尝试跳转到目标页面 (${i + 1}/${this.options.maxRetries})`);

      try {
        await this.page.goto(targetUrl, {
          waitUntil: 'networkidle',
          timeout: this.options.timeout,
        });

        await this.page.waitForLoadState('networkidle');

        const newUrl = this.page.url();
        if (newUrl.includes('/operation-web/drawManage')) {
          return true;
        }
      } catch (error) {
        logger.warn(`第 ${i + 1} 次跳转失败`, error.message);
        if (i === this.options.maxRetries - 1) {
          throw error;
        }
        await this.page.waitForTimeout(this.options.retryDelay);
      }
    }

    return false;
  }

  /**
   * 返回上一页
   */
  async goBack() {
    try {
      await this.page.goBack();
      await this.page.waitForTimeout(1000);
    } catch (error) {
      logger.error('返回上一页失败', error);
      throw error;
    }
  }
}

module.exports = NavigationManager;
