const logger = require('../utils/logger');

async function tabGoPage(page, targetUrl) {
  try {
    // 使用当前页面直接跳转
    await page.goto(targetUrl, {
      timeout: 30000,
      waitUntil: 'domcontentloaded',
    });

    // 等待页面完全加载
    await page.waitForLoadState('networkidle', { timeout: 15000 });
    return true;
  } catch (e) {
    logger.error(`跳转页面失败: ${e.message}`);
    return false;
  }
}

/**
 * 返回上一页
 */
async function goBack(page) {
  try {
    await page.goBack();
    await page.waitForTimeout(1000);
  } catch (error) {
    logger.error(`返回上一页失败： ${error.message}`);
    throw error;
  }
}

module.exports = {
  tabGoPage,
  goBack,
};
