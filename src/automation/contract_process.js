const logger = require('../utils/logger');
const SELECTORS = require('../utils/config/selectors');
const { downloadFromURL } = require('../utils/pdfDownloader');

async function getMainContract(page, downloadDir) {
  // 等待关联合同表格加载
  const contractTable = page.locator(SELECTORS.detail.guanLianContract);
  await contractTable.waitFor({ state: 'visible', timeout: 10000 });

  // 获取所有行
  const rows = contractTable.locator('tr');
  const rowCount = await rows.count();

  if (rowCount === 0) {
    logger.warn('未找到关联合同数据');
    return;
  }

  const contractFileMap = [
    {
      name: '保理服务合同',
      rule: '保理服务合同',
    },
    {
      name: '企业征信授权书',
      rule: '企业征信授权书',
    },
    {
      name: '企业征信授权书',
      rule: '企业征信授权书',
    },
    {
      name: '最高额保证担保合同',
      rule: '最高额保证担保合同',
    },
    {
      name: '票据让与担保合同',
      rule: '票据让与担保合同',
    },
  ];

  // 遍历每一行并检查第二个td的内容
  for (let i = 0; i < rowCount; i++) {
    const row = rows.nth(i);

    // 获取第二个td的文本内容
    const secondTd = row.locator('td').nth(1);
    const thirdTd = row.locator('td').nth(2);
    const contractName = await secondTd.textContent();
    const contractNo = await thirdTd.textContent();

    for (let i = 0; i < contractFileMap.length; i++) {
      const name = contractFileMap[i].name;
      if (contractName.includes(name)) {
        const downloadLink = secondTd.locator('a').first();
        const href = await downloadLink.getAttribute('href');

        const success = await downloadFromURL(
          href,
          page,
          downloadDir,
          `${name}-${contractNo}`,
        );
        if (!success) {
          logger.warn(`应收账款转让通知函下载失败: ${contractNo}`);
        }
      }
    }
  }
}

module.exports = {
  getMainContract,
};
