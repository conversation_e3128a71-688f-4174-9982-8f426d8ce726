const logger = require('../utils/logger');
const SELECTORS = require('../utils/config/selectors');

/**
 * 登录管理器 - 处理登录相关逻辑
 */
class LoginManager {
  constructor(elementOp, navigationManager, configInstance) {
    this.elementOp = elementOp;
    this.navigationManager = navigationManager;
    this.configInstance = configInstance;
  }

  /**
   * 执行完整登录流程
   */
  async performLogin() {
    const targetUrl =
      'https://gfconsole-wsuat.syitservice.com/web-tenant/index.html#/operation-web/drawManage';
    const encodedTargetUrl = encodeURIComponent(targetUrl);
    const loginUrl = `https://gfconsole-wsuat.syitservice.com/common/#/login?url=${encodedTargetUrl}`;

    // 访问登录页面
    await this.navigationManager.goToLogin(loginUrl);

    // 等待并填写登录表单
    await this.fillLoginForm();

    // 执行登录
    await this.submitLogin();

    // 等待登录成功并跳转
    await this.waitForLoginSuccess();

    // 确保跳转到目标页面
    const success = await this.navigationManager.ensureTargetPage(targetUrl);
    if (!success) {
      throw new Error('无法跳转到目标页面');
    }

    logger.success('登录成功，已跳转到目标页面');
  }

  /**
   * 填写登录表单
   */
  async fillLoginForm() {
    await this.elementOp.waitForElement(SELECTORS.login.username);
    await this.elementOp.waitForElement(SELECTORS.login.password);

    const account = this.configInstance.get('customSettings.account');
    const password = this.configInstance.get('customSettings.userPassword');

    if (!account || !password) {
      logger.warn('配置中缺少登录凭据，请手动完成登录');
      logger.info('请在浏览器中手动输入用户名和密码，然后点击登录按钮');
      return; // 跳过自动填写，让用户手动操作
    }

    await this.elementOp.fillInput(SELECTORS.login.username, account);
    await this.elementOp.fillInput(SELECTORS.login.password, password);
  }

  /**
   * 提交登录
   */
  async submitLogin() {
    const account = this.configInstance.get('customSettings.account');
    const password = this.configInstance.get('customSettings.userPassword');

    if (!account || !password) {
      logger.info('等待用户登录...');
      return; // 跳过自动点击登录按钮
    }
    await this.elementOp.click(SELECTORS.login.loginBtn);
  }

  /**
   * 等待登录成功
   */
  async waitForLoginSuccess() {
    await this.navigationManager.waitForLoginSuccess();
    logger.info('登录验证成功');
  }
}

module.exports = LoginManager;
