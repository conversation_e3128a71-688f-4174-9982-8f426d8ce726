const { program } = require('commander');
const fs = require('fs');
const path = require('path');
const logger = require('./utils/logger');
const config = require('./utils/config');

// 获取脚本目录
const scriptsDir = path.join(__dirname, 'scripts');

/**
 * 获取所有可用的脚本
 */
function getAvailableScripts() {
  try {
    const files = fs.readdirSync(scriptsDir);
    return files
      .filter((file) => file.endsWith('.js'))
      .map((file) => file.replace('.js', ''));
  } catch (error) {
    logger.error('读取脚本目录失败', error);
    return [];
  }
}

/**
 * 运行指定的脚本
 */
async function runScript(scriptName) {
  const scriptPath = path.join(scriptsDir, `${scriptName}.js`);

  if (!fs.existsSync(scriptPath)) {
    logger.error(`脚本不存在: ${scriptName}`);
    console.log('\n可用的脚本:');
    getAvailableScripts().forEach((script) => {
      console.log(`  - ${script}`);
    });
    process.exit(1);
  }

  try {
    logger.scriptStart(scriptName);
    const startTime = Date.now();

    // 动态加载并运行脚本
    const script = require(scriptPath);

    // 检查脚本是否导出了 run 函数
    if (typeof script.run === 'function') {
      await script.run();
    } else if (typeof script === 'function') {
      await script();
    } else {
      logger.error('脚本格式错误：必须导出 run 函数或默认函数');
      process.exit(1);
    }

    const duration = Date.now() - startTime;
    logger.scriptEnd(scriptName, true, duration);
  } catch (error) {
    logger.error('脚本执行失败', error);
    logger.scriptEnd(scriptName, false);
    process.exit(1);
  }
}

// 设置命令行参数
program
  .name('gf-auto-archive')
  .description('国富自动化归档工具')
  .version('1.0.0');

// 运行脚本命令
program.command('run <script>').description('运行指定的脚本').action(runScript);

// 列出所有脚本
program
  .command('list')
  .description('列出所有可用的脚本')
  .action(() => {
    const scripts = getAvailableScripts();
    if (scripts.length === 0) {
      console.log('没有找到可用的脚本');
    } else {
      console.log('可用的脚本:');
      scripts.forEach((script) => {
        console.log(`  - ${script}`);
      });
    }
  });

// 查看配置
program
  .command('config')
  .description('查看当前配置')
  .action(() => {
    const configInstance = config.getInstance();
    console.log('当前配置:');
    console.log(JSON.stringify(configInstance.getAll(), null, 2));
    console.log(
      `\n配置文件位置: ${path.join(__dirname, '../data/config.json')}`,
    );
  });

// 重置配置
program
  .command('config-reset')
  .description('重置配置为默认值')
  .action(() => {
    const configInstance = config.getInstance();
    configInstance.reset();
    console.log('配置已重置为默认值');
  });

// 默认命令（无参数时运行）
program.action(() => {
  console.log('欢迎使用网页自动化脚本工具！\n');
  console.log('使用方法:');
  console.log('  node src/index.js run <script>  - 运行指定脚本');
  console.log('  node src/index.js list          - 列出所有脚本');
  console.log('  node src/index.js config        - 查看配置');
  console.log('  node src/index.js --help        - 查看帮助\n');

  const scripts = getAvailableScripts();
  if (scripts.length > 0) {
    console.log('可用的脚本:');
    scripts.forEach((script) => {
      console.log(`  - ${script}`);
    });
  }
});

// 解析命令行参数
program.parse(process.argv);

// 如果没有提供任何参数，显示帮助信息
if (!process.argv.slice(2).length) {
  program.outputHelp();
}
